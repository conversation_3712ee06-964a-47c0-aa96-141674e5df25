package com.example.warehousemanagement.ui.inventory

import android.app.DatePickerDialog
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.warehousemanagement.R
import com.example.warehousemanagement.databinding.ActivityInventoryBinding
import com.example.warehousemanagement.ui.form.FormActivity
import java.text.SimpleDateFormat
import java.util.*

class InventoryActivity : AppCompatActivity() {

    private lateinit var binding: ActivityInventoryBinding
    private lateinit var adapter: InventoryAdapter
    private val inventoryItems = mutableListOf<InventoryItem>()
    private val calendar = Calendar.getInstance()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInventoryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupRecyclerView()
        setupDatePickers()
        loadDummyData()
    }

    private fun setupUI() {
        // 设置确认按钮点击事件
        binding.btnConfirm.setOnClickListener {
            val intent = Intent(this, FormActivity::class.java)
            startActivity(intent)
        }

        // 设置搜索按钮点击事件
        binding.btnSearch.setOnClickListener {
            // 在实际应用中，这里应该根据日期范围过滤数据
            // 这里简单地刷新列表
            adapter.notifyDataSetChanged()
        }

        // 设置底部导航栏
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_in -> true
                R.id.navigation_query -> {
                    // 切换到查询模式
                    true
                }
                R.id.navigation_recycle -> {
                    // 回收功能暂未实现
                    true
                }
                R.id.navigation_report -> {
                    // 报表功能暂未实现
                    true
                }
                else -> false
            }
        }

        // 默认选中入库选项
        binding.bottomNavigation.selectedItemId = R.id.navigation_in
    }

    private fun setupRecyclerView() {
        adapter = InventoryAdapter(inventoryItems) { item ->
            // 点击查看按钮时的处理
            val intent = Intent(this, FormActivity::class.java)
            intent.putExtra("item_id", item.id)
            intent.putExtra("view_only", true)
            startActivity(intent)
        }

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }

    private fun setupDatePickers() {
        // 设置开始日期选择器
        binding.tvDateFrom.setOnClickListener {
            showDatePicker(true)
        }

        // 设置结束日期选择器
        binding.tvDateTo.setOnClickListener {
            showDatePicker(false)
        }

        // 初始化日期显示
        binding.tvDateFrom.text = dateFormat.format(calendar.time)
        binding.tvDateTo.text = dateFormat.format(calendar.time)
    }

    private fun showDatePicker(isFromDate: Boolean) {
        val dateListener = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            calendar.set(Calendar.YEAR, year)
            calendar.set(Calendar.MONTH, month)
            calendar.set(Calendar.DAY_OF_MONTH, day)

            val formattedDate = dateFormat.format(calendar.time)
            if (isFromDate) {
                binding.tvDateFrom.text = formattedDate
            } else {
                binding.tvDateTo.text = formattedDate
            }
        }

        DatePickerDialog(
            this,
            dateListener,
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun loadDummyData() {
        // 加载模拟数据
        val currentDate = Calendar.getInstance()
        val chineseDateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINA)

        for (i in 1..10) {
            val id = "WH" + String.format("%08d", i)
            val date = chineseDateFormat.format(currentDate.time)
            inventoryItems.add(InventoryItem(id, date))
            currentDate.add(Calendar.DAY_OF_MONTH, -1) // 每条记录日期减1天
        }

        adapter.notifyDataSetChanged()
    }
}

// 数据模型类
data class InventoryItem(val id: String, val date: String)