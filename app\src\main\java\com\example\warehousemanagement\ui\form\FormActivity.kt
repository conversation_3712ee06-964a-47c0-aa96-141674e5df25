package com.example.warehousemanagement.ui.form

import android.app.DatePickerDialog
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.warehousemanagement.R
import com.example.warehousemanagement.databinding.ActivityFormBinding
import java.text.SimpleDateFormat
import java.util.*

class FormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFormBinding
    private val calendar = Calendar.getInstance()
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private var isViewOnly = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 检查是否是只读模式
        isViewOnly = intent.getBooleanExtra("view_only", false)

        setupUI()
        setupDatePicker()
        loadItemData()
    }

    private fun setupUI() {
        // 设置底部导航栏
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_in -> true
                R.id.navigation_query -> {
                    // 切换到查询模式
                    finish()
                    true
                }
                R.id.navigation_recycle -> {
                    // 回收功能暂未实现
                    true
                }
                R.id.navigation_report -> {
                    // 报表功能暂未实现
                    true
                }
                else -> false
            }
        }

        // 默认选中入库选项
        binding.bottomNavigation.selectedItemId = R.id.navigation_in

        // 设置取消按钮点击事件
        binding.btnCancel.setOnClickListener {
            finish()
        }

        // 设置确认按钮点击事件
        binding.btnConfirm.setOnClickListener {
            if (validateForm()) {
                saveFormData()
                Toast.makeText(this, "保存成功", Toast.LENGTH_SHORT).show()
                finish()
            }
        }

        // 如果是只读模式，禁用编辑功能
        if (isViewOnly) {
            disableEditing()
        }
    }

    private fun setupDatePicker() {
        // 设置日期选择器
        binding.etDate.setOnClickListener {
            if (!isViewOnly) {
                showDatePicker()
            }
        }

        // 初始化日期显示
        binding.etDate.setText(dateFormat.format(calendar.time))
    }

    private fun showDatePicker() {
        val dateListener = DatePickerDialog.OnDateSetListener { _, year, month, day ->
            calendar.set(Calendar.YEAR, year)
            calendar.set(Calendar.MONTH, month)
            calendar.set(Calendar.DAY_OF_MONTH, day)

            val formattedDate = dateFormat.format(calendar.time)
            binding.etDate.setText(formattedDate)
        }

        DatePickerDialog(
            this,
            dateListener,
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        ).show()
    }

    private fun loadItemData() {
        // 获取传入的item_id
        val itemId = intent.getStringExtra("item_id")

        // 如果有item_id，加载对应的数据
        if (itemId != null) {
            // 在实际应用中，这里应该从数据库或服务器加载数据
            // 这里简单地设置一些模拟数据
            binding.etCode.setText(itemId)
            binding.etStatus.setText("正常")
            binding.etItem1.setText("物品1")
            binding.etItem2.setText("物品2")
            binding.etItem3.setText("物品3")
            binding.etItem4.setText("物品4")
            binding.etItem5.setText("物品5")
        } else {
            // 新建表单，生成一个新的编码
            val newId = "WH" + String.format("%08d", Random().nextInt(100000))
            binding.etCode.setText(newId)
        }
    }

    private fun validateForm(): Boolean {
        // 简单的表单验证
        if (binding.etCode.text.toString().trim().isEmpty()) {
            binding.tilCode.error = "请输入编号"
            return false
        }

        if (binding.etStatus.text.toString().trim().isEmpty()) {
            binding.tilStatus.error = "请输入状态"
            return false
        }

        return true
    }

    private fun saveFormData() {
        // 在实际应用中，这里应该将数据保存到数据库或发送到服务器
        // 这里简单地打印数据
        val code = binding.etCode.text.toString().trim()
        val status = binding.etStatus.text.toString().trim()
        val date = binding.etDate.text.toString().trim()
        val item1 = binding.etItem1.text.toString().trim()
        val item2 = binding.etItem2.text.toString().trim()
        val item3 = binding.etItem3.text.toString().trim()
        val item4 = binding.etItem4.text.toString().trim()
        val item5 = binding.etItem5.text.toString().trim()

        println("保存表单数据：")
        println("编号: $code")
        println("状态: $status")
        println("日期: $date")
        println("条目1: $item1")
        println("条目2: $item2")
        println("条目3: $item3")
        println("条目4: $item4")
        println("条目5: $item5")
    }

    private fun disableEditing() {
        // 禁用所有输入框的编辑功能
        binding.etCode.isEnabled = false
        binding.etStatus.isEnabled = false
        binding.etDate.isEnabled = false
        binding.etItem1.isEnabled = false
        binding.etItem2.isEnabled = false
        binding.etItem3.isEnabled = false
        binding.etItem4.isEnabled = false
        binding.etItem5.isEnabled = false

        // 隐藏按钮
        binding.btnConfirm.visibility = View.GONE
        binding.btnCancel.text = getString(R.string.confirm)
    }
}