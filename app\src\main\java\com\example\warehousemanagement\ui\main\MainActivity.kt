package com.example.warehousemanagement.ui.main

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import com.example.warehousemanagement.R
import com.example.warehousemanagement.databinding.ActivityMainBinding
import com.example.warehousemanagement.ui.details.DetailsActivity
import com.example.warehousemanagement.ui.inventory.InventoryActivity

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupNavigation()
    }

    private fun setupUI() {
        // 设置入库扫描卡片点击事件
        binding.cardScanIn.setOnClickListener {
            val intent = Intent(this, InventoryActivity::class.java)
            intent.putExtra("mode", "scan_in")
            startActivity(intent)
        }

        // 设置查询出库卡片点击事件
        binding.cardQueryOut.setOnClickListener {
            val intent = Intent(this, DetailsActivity::class.java)
            intent.putExtra("mode", "query_out")
            startActivity(intent)
        }
    }

    private fun setupNavigation() {
        // 设置底部导航栏选择事件
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_in -> {
                    val intent = Intent(this, InventoryActivity::class.java)
                    intent.putExtra("mode", "scan_in")
                    startActivity(intent)
                    true
                }
                R.id.navigation_query -> {
                    val intent = Intent(this, DetailsActivity::class.java)
                    intent.putExtra("mode", "query")
                    startActivity(intent)
                    true
                }
                R.id.navigation_recycle -> {
                    // 回收功能暂未实现
                    true
                }
                R.id.navigation_report -> {
                    // 报表功能暂未实现
                    true
                }
                else -> false
            }
        }

        // 默认选中入库选项
        binding.bottomNavigation.selectedItemId = R.id.navigation_in
    }
}