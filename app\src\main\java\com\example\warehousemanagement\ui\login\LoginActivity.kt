package com.example.warehousemanagement.ui.login

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.warehousemanagement.R
import com.example.warehousemanagement.databinding.ActivityLoginBinding
import com.example.warehousemanagement.ui.main.MainActivity

class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
    }

    private fun setupUI() {
        // 设置登录按钮点击事件
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            // 简单的输入验证
            if (username.isEmpty()) {
                binding.tilUsername.error = "请输入用户名"
                return@setOnClickListener
            } else {
                binding.tilUsername.error = null
            }

            if (password.isEmpty()) {
                binding.tilPassword.error = "请输入密码"
                return@setOnClickListener
            } else {
                binding.tilPassword.error = null
            }

            // 模拟登录验证
            if (validateCredentials(username, password)) {
                // 登录成功，跳转到主界面
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                finish() // 结束登录界面，防止返回
            } else {
                // 登录失败，显示错误信息
                Toast.makeText(this, "用户名或密码错误", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 模拟验证用户名和密码
    private fun validateCredentials(username: String, password: String): Boolean {
        // 在实际应用中，这里应该连接到服务器或本地数据库进行验证
        // 这里简单地检查用户名和密码是否为admin
        return username == "admin" && password == "admin"
    }
}