package com.example.warehousemanagement.ui.details

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.warehousemanagement.R
import com.example.warehousemanagement.databinding.ActivityDetailsBinding
import com.example.warehousemanagement.ui.form.FormActivity
import java.text.SimpleDateFormat
import java.util.*

class DetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDetailsBinding
    private lateinit var adapter: DetailAdapter
    private val detailItems = mutableListOf<DetailItem>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        setupRecyclerView()
        loadDummyData()
    }

    private fun setupUI() {
        // 设置底部导航栏
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.navigation_in -> {
                    // 切换到入库模式
                    finish()
                    true
                }
                R.id.navigation_query -> true
                R.id.navigation_recycle -> {
                    // 回收功能暂未实现
                    true
                }
                R.id.navigation_report -> {
                    // 报表功能暂未实现
                    true
                }
                else -> false
            }
        }

        // 默认选中查询选项
        binding.bottomNavigation.selectedItemId = R.id.navigation_query

        // 设置底部编码信息
        binding.tvCodeInfo.text = "0123456789"
    }

    private fun setupRecyclerView() {
        adapter = DetailAdapter(detailItems) { item ->
            // 点击查看按钮时的处理
            val intent = Intent(this, FormActivity::class.java)
            intent.putExtra("item_id", item.id)
            intent.putExtra("view_only", true)
            startActivity(intent)
        }

        binding.recyclerView.layoutManager = LinearLayoutManager(this)
        binding.recyclerView.adapter = adapter
    }

    private fun loadDummyData() {
        // 加载模拟数据
        val currentDate = Calendar.getInstance()
        val chineseDateFormat = SimpleDateFormat("yyyy年MM月dd日 HH:mm", Locale.CHINA)

        for (i in 1..15) {
            val id = "WH" + String.format("%08d", i)
            val date = chineseDateFormat.format(currentDate.time)
            detailItems.add(DetailItem(id, date))
            currentDate.add(Calendar.HOUR, -2) // 每条记录时间减2小时
        }

        adapter.notifyDataSetChanged()
    }
}

// 数据模型类
data class DetailItem(val id: String, val date: String)