package com.example.warehousemanagement.ui.details

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.example.warehousemanagement.databinding.ItemDetailBinding

class DetailAdapter(
    private val items: List<DetailItem>,
    private val onViewClick: (DetailItem) -> Unit
) : RecyclerView.Adapter<DetailAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemDetailBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.bind(item)
    }

    override fun getItemCount(): Int = items.size

    inner class ViewHolder(private val binding: ItemDetailBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(item: DetailItem) {
            binding.tvItemCode.text = item.id
            binding.tvItemDate.text = item.date

            binding.btnItemView.setOnClickListener {
                onViewClick(item)
            }
        }
    }
}