# app/src/main/java/densowave/com/DensoScannerSDK_demo 文件功能说明

## BarcodeActivity.java
该类继承自 `BaseActivity` 并实现 `BarcodeDataDelegate` 接口，用于处理条形码扫描相关操作。主要功能包括初始化 RecyclerView 显示扫描结果、控制扫描开始和停止按钮、处理扫描回调事件将结果添加到列表中。

## BarcodeRecyclerViewAdapter.java
控制用于显示条形码标签的 RecyclerView 的生成。管理条形码数据集，处理数据的添加、清除操作，以及设置列表项的背景颜色和数据绑定。

## BaseActivity.java
作为公共控制类，继承自 `AppCompatActivity` 并实现 `ScannerStatusListener` 接口。管理 Activity 栈，处理扫描器连接状态变化事件，提供显示消息、断开扫描器连接、启动后台服务等功能。

## BeepAudioTracks.java
负责收集和管理扫描器蜂鸣声的音频轨道。预先生成音频轨道，提供播放、停止、释放音频轨道等功能。

## CompareMasterActivity.java
继承自 `BaseActivity` 并实现 `RFIDDataDelegate` 接口，用于比较主文件数据和扫描的 RFID 标签数据。支持选择主文件、开始和停止读取标签、输出比较结果等功能。

## FileUtils.java
提供文件操作的工具方法，包括检查文件名有效性、将显示的列表 UII 保存为 CSV 文件、读取主文件数据、输出比较结果文件等功能。

## InventoryActivity.java
继承自 `BaseActivity` 并实现 `RFIDDataDelegate` 接口，是库存界面的 Activity。负责初始化 RecyclerView 显示扫描的 RFID 标签数据，控制读取开始和停止，处理标签读取回调事件，支持文件输出功能。

## LocateTagActivity.java
继承自 `BaseActivity` 并实现 `RFIDDataDelegate` 接口，用于定位标签。管理搜索标签的 UII，设置读取功率和匹配方法，处理标签读取事件，提供搜索标签和导航功能。

## MainActivity.java
作为应用启动时的主 Activity，对应 Home 屏幕。继承自 `BaseActivity` 并实现 `ScannerAcceptStatusListener` 接口。负责设置应用版本，处理扫描器连接事件，提供跳转到其他 Activity 的功能。

## NamePlateGenerator.java
提供一个静态方法 `from`，用于从 QR 码数据中提取设备名称和序列号，生成铭牌信息。

## NavigationTagActivity.java
继承自 `BaseActivity` 并实现 `BarcodeDataDelegate` 接口，用于导航标签操作。可处理条形码扫描事件，实现标签的注册、点亮和熄灭功能，与数据库交互存储标签信息。

## NumberInputFragment.java
一个对话框 Fragment，用于输入数字。可设置标题和初始数字，提供输入监听接口，处理输入数字的转换和验证。

## NumberPickerFragment.java
一个对话框 Fragment，用于选择数字。可设置标题、最小值、最大值和初始值，提供选择监听接口，返回用户选择的数字。

## PreFiltersActivity.java
用于设置和管理 RFID 扫描器的预过滤器。可编辑过滤器的 Bank、Offset 和 Pattern，将过滤器设置到扫描器，清除和加载过滤器设置。

## RapidReadActivity.java
继承自 `BaseActivity` 并实现 `RFIDDataDelegate` 接口，用于快速读取 RFID 标签。可显示读取的标签数量、读取时间和每秒读取标签数，支持开始、停止和清除读取数据操作。

## ServiceParam.java
实现 `Serializable` 接口，用于在服务间传递 `CommScanner` 对象。

## SettingsActivity.java
用于设置扫描器的各项参数，包括蜂鸣器音量、触发模式、极化方式等。读取和保存扫描器的配置信息，处理按键和点击事件。

## SimpleDatabaseHelper.java
继承自 `SQLiteOpenHelper`，用于管理 SQLite 数据库。创建和升级数据库表 `TAGForLight2`，处理数据库的打开和初始化操作。

## StringInputFragment.java
一个对话框 Fragment，用于输入字符串。可设置输入类型、标题和初始字符串，提供输入监听接口，处理输入字符串的获取。

## StringSelectorFragment.java
一个对话框 Fragment，用于选择字符串。可设置标题和选项列表，提供选择监听接口，返回用户选择的字符串和索引。