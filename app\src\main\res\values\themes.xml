<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.WarehouseManagement" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <!-- Customize your theme here. -->
        <item name="android:windowBackground">@color/background_gray</item>
    </style>
    
    <style name="Theme.WarehouseManagement.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <style name="AppButton" parent="Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/white</item>
        <item name="android:padding">12dp</item>
        <item name="android:textSize">16sp</item>
    </style>
    
    <style name="AppButton.Blue">
        <item name="backgroundTint">@color/button_blue</item>
    </style>
    
    <style name="AppButton.Gray">
        <item name="backgroundTint">@color/button_gray</item>
    </style>
    
    <style name="AppTextField" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/primary</item>
    </style>
</resources>