package densowave.com.DensoScannerSDK_demo;

import android.os.Bundle;
import android.os.Handler;
import androidx.appcompat.app.AppCompatActivity;
import android.view.Gravity;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.view.KeyEvent;

import com.densowave.scannersdk.Common.CommException;
import com.densowave.scannersdk.Common.CommManager;
import com.densowave.scannersdk.Common.CommScanner;
import com.densowave.scannersdk.Common.CommStatusChangedEvent;
import com.densowave.scannersdk.Const.CommConst;
import com.densowave.scannersdk.Listener.RFIDDataDelegate;
import com.densowave.scannersdk.Listener.ScannerAcceptStatusListener;
import com.densowave.scannersdk.Listener.ScannerStatusListener;
import com.densowave.scannersdk.RFID.RFIDDataReceivedEvent;

// 导入正确的R类
import com.example.warehousemanagement.R;

import java.util.ArrayList;
import java.util.List;

/**
 * 简化版RFID扫描Activity
 * 功能：
 * 1. 显示设备连接状态
 * 2. 自动开启/关闭扫描
 * 3. 在TextView中显示扫描结果，用逗号分隔
 * 4. 每次扫描清空上次结果
 */
public class SimpleRfidActivity extends AppCompatActivity implements RFIDDataDelegate, ScannerStatusListener, ScannerAcceptStatusListener {

    private TextView tvConnectionStatus; // 设备连接状态标签
    private TextView tvScanResults;      // 扫描结果显示区域
    private Button btnConnect;           // 连接设备按钮
    private Handler handler = new Handler();
    private boolean scannerConnectedOnCreate = false;
    
    // 从BaseActivity移植过来的变量
    public static CommScanner commScanner;
    public static boolean scannerConnected = false;
    private Toast ts = null;
    private boolean topActivity = false;
    
    // 定义错误消息字符串常量，替代R.string引用
    private static final String MSG_COMMUNICATION_ERROR = "通信错误，请检查设备连接";
    private static final String MSG_NO_CONNECTION = "设备未连接，请先连接设备";
    private static final String MSG_CONNECTING = "正在连接设备，请稍候...";
    private static final String MSG_CONNECT_SUCCESS = "设备连接成功";
    private static final String MSG_CONNECT_FAILED = "设备连接失败，请重试";
    
    // 连接重试相关变量
    private Handler retryHandler = new Handler();
    private int retryCount = 0;
    private static final int MAX_RETRY_COUNT = 3;
    private static final int RETRY_DELAY_MS = 2000; // 2秒后重试
    private boolean isConnecting = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_simple_rfid);

        // 初始化UI组件
        tvConnectionStatus = findViewById(R.id.tv_connection_status);
        tvScanResults = findViewById(R.id.tv_scan_results);
        btnConnect = findViewById(R.id.btn_connect);
        
        // 设置连接按钮点击事件
        btnConnect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!scannerConnected && !isConnecting) {
                    // 如果未连接且不在连接过程中，则开始连接
                    connectToScanner();
                } else if (scannerConnected) {
                    // 如果已连接，则断开连接
                    disconnectCommScanner();
                    updateConnectionStatus();
                    btnConnect.setText("连接设备");
                }
            }
        });

        // 检查设备连接状态
        scannerConnectedOnCreate = isCommScanner();
        updateConnectionStatus();

        // 如果设备已连接，设置数据委托并开始扫描
        if (scannerConnectedOnCreate) {
            try {
                getCommScanner().getRFIDScanner().setDataDelegate(this);
                startRfidScan();
                btnConnect.setText("断开连接");
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
            }
        } else {
            showMessage(MSG_NO_CONNECTION);
            btnConnect.setText("连接设备");
        }
    }

    /**
     * 更新连接状态显示
     */
    private void updateConnectionStatus() {
        if (scannerConnectedOnCreate) {
            tvConnectionStatus.setText("设备状态: 已连接");
            btnConnect.setText("断开连接");
        } else {
            tvConnectionStatus.setText("设备状态: 未连接");
            btnConnect.setText("连接设备");
        }
    }
    
    /**
     * 连接到扫描器设备
     */
    private void connectToScanner() {
        isConnecting = true;
        retryCount = 0;
        showMessage(MSG_CONNECTING);
        tvConnectionStatus.setText("设备状态: 正在连接...");
        btnConnect.setEnabled(false);
        
        // 使用与MainActivity相同的方式连接扫描器
        CommManager.addAcceptStatusListener(this);
        CommManager.startAccept();
    }
    
    // 删除attemptConnection方法，因为不再使用

    /**
     * 处理连接失败的情况，实现重试机制
     */
    private void handleConnectionFailure() {
        if (retryCount < MAX_RETRY_COUNT) {
            retryCount++;
            showMessage(MSG_CONNECT_FAILED + "，第" + retryCount + "次重试...");
            
            // 延迟一段时间后重试
            retryHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // 使用与MainActivity相同的方式连接扫描器
                    CommManager.addAcceptStatusListener(SimpleRfidActivity.this);
                    CommManager.startAccept();
                }
            }, RETRY_DELAY_MS);
        } else {
            isConnecting = false;
            showMessage(MSG_CONNECT_FAILED + "，请检查设备并重试");
            tvConnectionStatus.setText("设备状态: 未连接");
            btnConnect.setText("连接设备");
        }
    }

    /**
     * 开始RFID扫描
     */
    private void startRfidScan() {
        if (scannerConnectedOnCreate) {
            try {
                // 清空上次扫描结果
                tvScanResults.setText("");
                // 开始扫描
                getCommScanner().getRFIDScanner().openInventory();
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
                e.printStackTrace();
            }
        }
    }

    /**
     * 停止RFID扫描
     */
    private void stopRfidScan() {
        if (scannerConnectedOnCreate) {
            try {
                getCommScanner().getRFIDScanner().close();
            } catch (Exception e) {
                showMessage(MSG_COMMUNICATION_ERROR);
                e.printStackTrace();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        
        // 中止连接请求
        CommManager.endAccept();
        CommManager.removeAcceptStatusListener(this);
    }
    
    @Override
    protected void onDestroy() {
        // 停止扫描并移除数据委托
        if (scannerConnectedOnCreate) {
            stopRfidScan();
            getCommScanner().getRFIDScanner().setDataDelegate(null);
        }
        
        // 清理Handler
        handler = null;
        retryHandler.removeCallbacksAndMessages(null);
        retryHandler = null;
        
        // 中止连接请求
        CommManager.endAccept();
        CommManager.removeAcceptStatusListener(this);
        
        super.onDestroy();
    }

    /**
     * 处理返回键事件
     */
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            // 停止扫描并移除数据委托
            if (scannerConnectedOnCreate) {
                stopRfidScan();
                getCommScanner().getRFIDScanner().setDataDelegate(null);
            }
            handler = null;
            finish();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    /**
     * 处理RFID数据接收事件
     */
    @Override
    public void onRFIDDataReceived(CommScanner scanner, final RFIDDataReceivedEvent rfidDataReceivedEvent) {
        // 在UI线程中处理数据
        handler.post(new Runnable() {
            @Override
            public void run() {
                // 清空上次结果
                tvScanResults.setText("");
                
                // 处理接收到的数据
                StringBuilder results = new StringBuilder();
                for (int i = 0; i < rfidDataReceivedEvent.getRFIDData().size(); i++) {
                    String data = "";
                    byte[] uii = rfidDataReceivedEvent.getRFIDData().get(i).getUII();
                    for (int loop = 0; loop < uii.length; loop++) {
                        data += String.format("%02X ", uii[loop]).trim();
                    }
                    
                    // 添加逗号分隔
                    if (results.length() > 0) {
                        results.append(", ");
                    }
                    results.append(data);
                }
                
                // 显示结果
                tvScanResults.setText(results.toString());
            }
        });
    }
    
    /**
     * 设置已连接的CommScanner
     * @param connectedCommScanner 设置已连接的CommScanner，如果为null，则将持有的CommScanner设为null
     */
    public void setConnectedCommScanner(CommScanner connectedCommScanner) {
        if (connectedCommScanner != null) {
            scannerConnected = true;
            connectedCommScanner.addStatusListener(this);
        } else {
            scannerConnected = false;
            if (commScanner != null) {
                commScanner.removeStatusListener(this);
            }
        }
        commScanner = connectedCommScanner;
    }

    /**
     * 获取CommScanner
     * 由于即使获取的CommScanner不为null也不一定总是连接的，
     * 使用isCommScanner来检查扫描器是否连接
     * @return CommScanner实例
     */
    public CommScanner getCommScanner() {
        return commScanner;
    }

    /**
     * 判断CommScanner
     * @return 如果CommScanner已连接返回true，否则返回false
     */
    public boolean isCommScanner() {
        return scannerConnected;
    }

    /**
     * 断开SP1连接
     */
    public void disconnectCommScanner() {
        if (commScanner != null) {
            try {
                commScanner.close();
                commScanner.removeStatusListener(this);
                scannerConnected = false;
                commScanner = null;
            } catch (CommException e) {
                this.showMessage(e.getMessage());
            }
        }
    }

    /**
     * 显示Toast消息
     * @param msg 消息内容
     */
    public synchronized void showMessage(String msg) {
        if (ts != null) {
            ts.cancel();
        }
        ts = Toast.makeText(this, msg, Toast.LENGTH_SHORT);
        ts.setGravity(Gravity.CENTER, 0, 0);
        ts.show();
    }

    /**
     * 设置TOP-Activity
     * @param topActivity true:TOP-Activity false:非TOP-Activity
     */
    protected void setTopActivity(boolean topActivity) {
        this.topActivity = topActivity;
    }

    /**
     * 当扫描器连接状态改变时的事件处理
     * @param scanner 扫描器
     * @param state 状态
     */
    @Override
    public void onScannerStatusChanged(CommScanner scanner, CommStatusChangedEvent state) {
        // 当扫描器断开连接时，commScanner将不会连接
        // 由于此事件处理是异步调用的，如果立即将commScanner设为null，可能会在处理过程中导致空指针异常
        // 为防止这种情况，保留实例并使用标志监控连接状态
        CommConst.ScannerStatus scannerStatus = state.getStatus();
        if (scanner == commScanner && scannerStatus.equals(CommConst.ScannerStatus.CLOSE_WAIT)) {
            // 当检测到断开连接状态时，终止除TOP屏幕外的所有Activity
            SimpleRfidActivity.this.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if(scannerConnected) {
                        // 显示断开连接消息
                        showMessage(MSG_NO_CONNECTION);

                        scannerConnected = false;
                        scannerConnectedOnCreate = false;
                        updateConnectionStatus();
                        
                        // 如果不是用户主动断开连接，可以尝试自动重连
                        if (!isConnecting) {
                            showMessage("设备连接已断开，正在尝试重新连接...");
                            connectToScanner();
                        }
                    }
                }
            });
        }
    }

    /**
     * SP1 连接事件回调
     * @param scanner 扫描器实例
     */
    @Override
    public void OnScannerAppeared(CommScanner scanner) {
        boolean successFlag = false;
        try {
            scanner.claim();
            // 中止连接请求
            CommManager.endAccept();
            CommManager.removeAcceptStatusListener(this);
            successFlag = true;
        } catch (CommException e) {
            e.printStackTrace();
        }

        try {
            setConnectedCommScanner(scanner);
            final boolean finalSuccessFlag = successFlag;
            
            // 在UI线程中处理连接结果
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    isConnecting = false;
                    if (finalSuccessFlag) {
                        scannerConnectedOnCreate = true;
                        updateConnectionStatus();
                        showMessage(MSG_CONNECT_SUCCESS);
                        
                        // 设置数据委托并开始扫描
                        try {
                            getCommScanner().getRFIDScanner().setDataDelegate(SimpleRfidActivity.this);
                            startRfidScan();
                        } catch (Exception e) {
                            showMessage(MSG_COMMUNICATION_ERROR);
                        }
                    } else {
                        // 连接失败，尝试重试
                        handleConnectionFailure();
                    }
                    btnConnect.setEnabled(true);
                }
            });
        } catch (Exception e) {
            // 发生异常，尝试重试
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    isConnecting = false;
                    handleConnectionFailure();
                    btnConnect.setEnabled(true);
                }
            });
        }
    }
}